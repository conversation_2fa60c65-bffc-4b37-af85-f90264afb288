package com.asmtunis.procaisseinventory.auth.login.screens

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.keyframes
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInVertically
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.twotone.PublishedWithChanges
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.FloatingActionButton
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.windowsizeclass.WindowWidthSizeClass
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.auth.AuthViewModel
import com.asmtunis.procaisseinventory.auth.LogoView
import com.asmtunis.procaisseinventory.auth.base_config.HeaderStatus
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.auth.login.data.domaine.Utilisateur
import com.asmtunis.procaisseinventory.auth.login.text_validation.LoginTextValidationViewModel
import com.asmtunis.procaisseinventory.auth.login.text_validation.ValidationLoginEvent
import com.asmtunis.procaisseinventory.auth.spalsh_screen.data.check_license.domaine.BaseConfigLicenseCheck
import com.asmtunis.procaisseinventory.core.Globals
import com.asmtunis.procaisseinventory.core.connectivity.internet.NetworkViewModel
import com.asmtunis.procaisseinventory.core.ktor.Urls
import com.asmtunis.procaisseinventory.core.ktor.domaine.RemoteResponseState
import com.asmtunis.procaisseinventory.core.local_storage.datastore.viewmodel.DataViewModel
import com.asmtunis.procaisseinventory.core.navigation.AuthGraph
import com.asmtunis.procaisseinventory.core.navigation.BaseConfigRoute
import com.asmtunis.procaisseinventory.core.navigation.InventoryGraph
import com.asmtunis.procaisseinventory.core.navigation.LoginRoute
import com.asmtunis.procaisseinventory.core.navigation.ProcaisseGraph
import com.asmtunis.procaisseinventory.core.utils.UpdateLoadingStateUtils
import com.asmtunis.procaisseinventory.network_errors.domaine.NetworkError
import com.asmtunis.procaisseinventory.network_errors.view_model.NetworkErrorsViewModel
import com.asmtunis.procaisseinventory.setting.SettingViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.AppBar
import com.asmtunis.procaisseinventory.shared_ui_components.CustomModifiers.customWidth
import com.asmtunis.procaisseinventory.shared_ui_components.ToastKMM
import com.asmtunis.procaisseinventory.shared_ui_components.floatingActionButtonPosition
import com.asmtunis.procaisseinventory.shared_ui_components.showToast
import com.asmtunis.procaisseinventory.view_model.GetProCaisseDataViewModel
import com.asmtunis.procaisseinventory.view_model.GetProInventoryDataViewModel
import com.asmtunis.procaisseinventory.view_model.GetSharedDataViewModel
import com.asmtunis.procaisseinventory.view_model.MainViewModel
import com.dokar.sonner.ToastType
import com.dokar.sonner.rememberToasterState
import kotlinx.coroutines.launch
import kotlin.time.Duration

@Composable
fun LoginScreen(
    navigate: (route: Any) -> Unit,
    navigatePopUpTo: (route: Any, popUpTo: Any, isInclusive: Boolean) -> Unit,
    dataViewModel: DataViewModel,
    settingViewModel: SettingViewModel,
    authViewModel: AuthViewModel,
    getProCaisseDataViewModel: GetProCaisseDataViewModel,
    getProInventoryDataViewModel: GetProInventoryDataViewModel,
    networkViewModel: NetworkViewModel,
    getSharedDataViewModel: GetSharedDataViewModel,
    mainViewModel: MainViewModel,
    networkErrorsVM: NetworkErrorsViewModel,
) {
    val loginTextValidationViewModel: LoginTextValidationViewModel = hiltViewModel()
    val isConnected = networkViewModel.isConnected
    val validationLoginEvent = loginTextValidationViewModel.validationLoginEvent
    val toaster = rememberToasterState()
    ToastKMM(toaster = toaster, darkTheme = settingViewModel.isDarkTheme)

    val uiWindowState = settingViewModel.uiWindowState
    val windowSize = uiWindowState.windowSize!!//?:  WindowSizeClass(WindowWidthSizeClass.Compact, WindowHeightSizeClass.Compact)


   val logo = mainViewModel.logo

    val context = LocalContext.current
  //  val lifecycle = LocalLifecycleOwner.current.lifecycle
    val selectedBaseconfig: BaseConfig = dataViewModel.selectedBaseConfig

    val loginState = authViewModel.proLoginState

    val checkLicensestate = authViewModel.checkLicensestate

    val isDemo = selectedBaseconfig.designation_base== Globals.DEMO_BASE_CONFIG

    val setTokenState = authViewModel.setTokenState

    val exerciceCode = mainViewModel.exerciceList.firstOrNull()?.exerciceCode?: ""

    val isLoadingCommenSharedData = UpdateLoadingStateUtils.isLoadingCommenSharedData(getSharedDataViewModel = getSharedDataViewModel)


    val networkErrorsList = networkErrorsVM.networkErrorsList

    LaunchedEffect(key1 = Unit) {
        if(dataViewModel.getFirebaseToken().isNotEmpty() ) {
            authViewModel.setUserFireBaseToken(token = "", baseConfig = selectedBaseconfig)
        }


        //todo find a better way to execute getCommenSharedData
            getSharedDataViewModel.getCommenSharedData(baseConfig = selectedBaseconfig)

    }
    LaunchedEffect(key1 = setTokenState.error) {
        if(setTokenState.error != null) {

            //todo show message when token not reset
//            showToast(
//                context = context,
//                toaster = toaster,
//                message = context.getString(R.string.error) + " \n"+ context.resources.getString(R.string.error_token),
//                type =  ToastType.Error,
//            )
            authViewModel.resetTokenState()
        }
    }

    LaunchedEffect(key1 = validationLoginEvent) {

        if(validationLoginEvent == ValidationLoginEvent()) return@LaunchedEffect

        authViewModel.handleLoginEvents(
            validationLoginEvent = validationLoginEvent,
            context = context,
            toaster = toaster,
            navigate = { navigate(it) },
            selectedBaseconfig = selectedBaseconfig,
            restErrorVariables = { loginTextValidationViewModel.restErrorVariables() }
        )
    }

    LaunchedEffect(key1 = loginState) {

        if (loginState.error != null) {
            showToast(
                context = context,
                duration = Duration.INFINITE,
                toaster = toaster,
                message = context.resources.getString(R.string.server_error),
                type =  ToastType.Error,
            )
            authViewModel.loginFinshed()
            loginTextValidationViewModel.resetValidationLoginEvent()
            return@LaunchedEffect
        }
        if (loginState.loading || loginState.data == null) return@LaunchedEffect

        when (authViewModel.handleBaseConfig()) {
            HeaderStatus.EMPTY.header -> {
                navigatePopUpTo(BaseConfig, LoginRoute, true)

            }

            HeaderStatus.PROCAISSE_AND_INVENTORY.header,
            HeaderStatus.PROCAISSE.header,
            HeaderStatus.INVENTORY.header,
            -> {
                if (loginFailed(loginState.data)) {
                    showToast(
                        context = context,
                        toaster = toaster,
                        message = context.resources.getString(R.string.error) + " ${authViewModel.handleBaseConfig()} \n"+ context.resources.getString(R.string.info_identification_invalid),
                        type =  ToastType.Error,
                    )

                    authViewModel.loginFinshed()
                    loginTextValidationViewModel.resetValidationLoginEvent()
                    return@LaunchedEffect
                }
                // Login success
                if (authViewModel.getLoginHeader() == HeaderStatus.EMPTY.header) {
                    showToast(
                        context = context,
                        toaster = toaster,
                        message = context.resources.getString(R.string.server_error),
                        type =  ToastType.Error,
                    )
                    loginTextValidationViewModel.resetValidationLoginEvent()
                    return@LaunchedEffect
                }
                getSharedDataViewModel.getSharedData(
                    baseConfig = selectedBaseconfig,
                    utilisateur = loginState.data,
                )
                if (authViewModel.getLoginHeader() == HeaderStatus.PROCAISSE_AND_INVENTORY.header) {
                    getProCaisseDataViewModel.getProcaisseData(
                        baseConfig = selectedBaseconfig,
                        utilisateur = loginState.data,
                        exerciceCode = exerciceCode
                    )

                    getProInventoryDataViewModel.getProInventoryData(
                        baseConfig = selectedBaseconfig,
                        utilisateur = loginState.data,
                    )
                }
                else if (authViewModel.getLoginHeader() == HeaderStatus.INVENTORY.header) {
                    getProInventoryDataViewModel.getProInventoryData(
                        baseConfig = selectedBaseconfig,
                        utilisateur = loginState.data,
                    )
                }
                else if (authViewModel.getLoginHeader() == HeaderStatus.PROCAISSE.header) {
                    getProCaisseDataViewModel.getProcaisseData(
                        baseConfig = selectedBaseconfig,
                        utilisateur = loginState.data,
                        exerciceCode = exerciceCode
                    )
                }
                dataViewModel.saveIsLoggedIn(true)
                authViewModel.loginFinshed()
                if (authViewModel.navToInvOrProCaisse() == InventoryGraph ||
                    authViewModel.navToInvOrProCaisse() == ProcaisseGraph
                ) {
                    navigatePopUpTo(authViewModel.navToInvOrProCaisse(), AuthGraph, true)
                }
                else navigate(authViewModel.navToInvOrProCaisse())
            }

            else -> {
                navigate(BaseConfigRoute)
            }
        }
    }

    Scaffold(
        topBar = {
            AppBar(
                showNavIcon = true,
                onNavigationClick = { authViewModel.getUtilisateur(baseConfig = selectedBaseconfig) },
                title = selectedBaseconfig.designation_base,
                isConnected = networkViewModel.isConnected,
                baseConfig = selectedBaseconfig
            )
        },
        floatingActionButtonPosition = floatingActionButtonPosition(windowSize = windowSize),
        floatingActionButton = {
            val density = LocalDensity.current

            AnimatedVisibility(
                visible = /*baseConfigList.size > 1 && */!loginState.loading,
                enter = slideInVertically { with(density) { 40.dp.roundToPx() } } + fadeIn(),
                exit = fadeOut(animationSpec = keyframes { this.durationMillis = 120 }),
            ) {
                FloatingActionButton(
                    onClick = {
                        dataViewModel.saveIsLoggedIn(false)
                        authViewModel.loginFinshed()
                        navigate(BaseConfigRoute)
                    }
                ) {
                    Icon(
                        imageVector = Icons.TwoTone.PublishedWithChanges,
                        contentDescription = stringResource(id = R.string.add_Client_button),
                    )
                }
            }
        }
    ) { padding ->
        when (windowSize.widthSizeClass) {
            WindowWidthSizeClass.Compact -> {
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(padding)
                        .padding(start = 9.dp, end = 9.dp).verticalScroll(rememberScrollState()),
                    verticalArrangement = Arrangement.Top,
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {

                    LogoView(
                        logo = logo,
                        toaster = toaster,
                    )
                    Spacer(modifier = Modifier.height(18.dp))
//                    SubscribtionButton(
//                        productName = Globals.PRO_CAISSE_MOBILITY,
//                        isActivated = dataViewModel::getProcaisseActivationState,
//                        isSubscriptionSent = dataViewModel::getProcaisseSubscribtionSent,
//                        isDemo = isDemo,
//                        selectedBaseconfig = selectedBaseconfig,
//                        onClick =  { navigate(SubscribtionRoute) }
//                    )
//
//                    SubscribtionButton(
//                        productName = Globals.PRO_INVENTORY,
//                        isActivated = dataViewModel::getInventoryActivationState,
//                        isSubscriptionSent =  dataViewModel::getProInventorySubscribtionSent,
//                        isDemo = isDemo,
//                        selectedBaseconfig = selectedBaseconfig,
//                        onClick = { navigate(SubscribtionRoute) }
//                    )

                    SignInView(
                        isConnected = isConnected,
                        networkErrorsList = networkErrorsList,
                        isLoadingCommenSharedData = isLoadingCommenSharedData,
                        getSharedDataViewModel = getSharedDataViewModel,
                        loginTextValidationViewModel = loginTextValidationViewModel,
                        dataViewModel = dataViewModel,
                        networkViewModel = networkViewModel,
                        authViewModel = authViewModel,
                        selectedBaseconfig = selectedBaseconfig,
                        checkLicensestate = checkLicensestate,
                    )
                }
            }

            WindowWidthSizeClass.Expanded,
            WindowWidthSizeClass.Medium -> {
                Row(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(padding)
                        .padding(start = 9.dp, end = 9.dp),
                ) {
                    Column(
                        modifier = Modifier
                            .customWidth(0.45f)
                            //   .padding(padding)
                            .padding(start = 9.dp, end = 9.dp),
                        verticalArrangement = Arrangement.Top,
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        LogoView(
                            logo = logo,
                            toaster = toaster,
                        )

//                        SubscribtionButton(
//                            productName = Globals.PRO_CAISSE_MOBILITY,
//                            isActivated = dataViewModel::getProcaisseActivationState,
//                            isSubscriptionSent = dataViewModel::getProcaisseSubscribtionSent,
//                            isDemo = isDemo,
//                            selectedBaseconfig = selectedBaseconfig,
//                            onClick =  { navigate(SubscribtionRoute) }
//                        )
//
//                        SubscribtionButton(
//                            productName = Globals.PRO_INVENTORY,
//                            isActivated = dataViewModel::getInventoryActivationState,
//                            isSubscriptionSent =  dataViewModel::getProInventorySubscribtionSent,
//                            isDemo = isDemo,
//                            selectedBaseconfig = selectedBaseconfig,
//                            onClick = { navigate(SubscribtionRoute) }
//                        )
                    }

                    Spacer(modifier = Modifier.height(18.dp))
                    SignInView(
                        isConnected = isConnected,
                        networkErrorsList = networkErrorsList,
                        isLoadingCommenSharedData = isLoadingCommenSharedData,
                        getSharedDataViewModel = getSharedDataViewModel,
                        loginTextValidationViewModel = loginTextValidationViewModel,
                        dataViewModel = dataViewModel,
                        networkViewModel = networkViewModel,
                        authViewModel = authViewModel,
                        selectedBaseconfig = selectedBaseconfig,
                        checkLicensestate = checkLicensestate
                    )
                }


            }
            else -> {
                SignInView(
                    isConnected = isConnected,
                    networkErrorsList = networkErrorsList,
                    isLoadingCommenSharedData = isLoadingCommenSharedData,
                    getSharedDataViewModel = getSharedDataViewModel,
                    loginTextValidationViewModel = loginTextValidationViewModel,
                    dataViewModel = dataViewModel,
                    networkViewModel = networkViewModel,
                    authViewModel = authViewModel,
                    selectedBaseconfig = selectedBaseconfig,
                    checkLicensestate = checkLicensestate
                )
            }
        }

    }
}



@Composable
fun SignInView(
    isConnected: Boolean,
    networkErrorsList: List<NetworkError>,
    isLoadingCommenSharedData: Boolean,
    loginTextValidationViewModel: LoginTextValidationViewModel,
    getSharedDataViewModel: GetSharedDataViewModel,
    authViewModel: AuthViewModel,
    dataViewModel: DataViewModel,
    networkViewModel: NetworkViewModel,
    selectedBaseconfig: BaseConfig,
    checkLicensestate: RemoteResponseState<BaseConfigLicenseCheck>,
) {
         val scope = rememberCoroutineScope()
    Column(
        modifier =
            Modifier
                .fillMaxSize()
              //  .padding(padding)
                ,
        verticalArrangement = Arrangement.Top,
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        /*
        authViewModel.proCaisseLoginFinshed()
                authViewModel.proInventoryLoginFinshed()
         */


        //todo see whether to add dataViewModel.getProInventorySubscribtionSent() ||dataViewModel.getProcaisseSubscribtionSent()
        // oflline mode
        if (checkLicensestate.data != null) {
            val demandes = checkLicensestate.data.demandes
            if (demandes.isNotEmpty()) {
                Text(
                    text = stringResource(R.string.demande_en_attente, demandes.first().produit),
                    color = MaterialTheme.colorScheme.error,
                )
            }
        }

        val state = loginTextValidationViewModel.stateLogin
        val context = LocalContext.current

       // Spacer(modifier = Modifier.height(12.dp))




         if(isLoadingCommenSharedData) {
             CircularProgressIndicator()
         }
         else if(networkErrorsList.dropLastWhile { it.url == Urls.INSERT_USER_TOKEN }.isNotEmpty()) {
             OutlinedButton (
                 modifier = Modifier.fillMaxWidth().padding(start = 16.dp, end = 16.dp),
                 enabled = networkViewModel.isConnected,
                 onClick = {
                     scope.launch {
                         getSharedDataViewModel.getCommenSharedData(baseConfig = selectedBaseconfig)
                     }
                 },
             ) { Text(text = stringResource(id = R.string.sync_data)) }
         }
        else {
             ShowSignIn(
                 state = state,
                 isConnected = isConnected,
                 loginTextValidationViewModel = loginTextValidationViewModel,
                 dataViewModel = dataViewModel,
                 networkViewModel = networkViewModel,
                 authViewModel = authViewModel,
                 selectedBaseconfig = selectedBaseconfig,
             )
         }

        Spacer(Modifier.height(6.dp))
    }
}





fun loginFailed(loginState: Utilisateur?): Boolean = loginState!!.Login == ""



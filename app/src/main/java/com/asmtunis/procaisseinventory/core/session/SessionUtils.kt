package com.asmtunis.procaisseinventory.core.session

import android.util.Log
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

/**
 * Utility functions for session management operations.
 * Provides helper functions for date/time operations and session state management.
 */
object SessionUtils {
    
    private const val TAG = "SessionUtils"
    private const val DATE_FORMAT = "yyyy-MM-dd HH:mm:ss"
    private const val DATE_ONLY_FORMAT = "yyyy-MM-dd"
    private const val TIME_FORMAT = "HH:mm:ss"

    /**
     * Formats the current date and time for session creation
     * 
     * @return Current date and time in "yyyy-MM-dd HH:mm:ss" format
     */
    fun getCurrentDateTime(): String {
        return try {
            val dateFormat = SimpleDateFormat(DATE_FORMAT, Locale.getDefault())
            dateFormat.format(Date())
        } catch (e: Exception) {
            Log.e(TAG, "Error formatting current date time", e)
            ""
        }
    }

    /**
     * Formats the current date only for session validation
     * 
     * @return Current date in "yyyy-MM-dd" format
     */
    fun getCurrentDate(): String {
        return try {
            val dateFormat = SimpleDateFormat(DATE_ONLY_FORMAT, Locale.getDefault())
            dateFormat.format(Date())
        } catch (e: Exception) {
            Log.e(TAG, "Error formatting current date", e)
            ""
        }
    }

    /**
     * Extracts the date part from a datetime string
     * 
     * @param dateTime DateTime string in "yyyy-MM-dd HH:mm:ss" format
     * @return Date part in "yyyy-MM-dd" format
     */
    fun extractDateFromDateTime(dateTime: String): String {
        return try {
            if (dateTime.length >= 10) {
                dateTime.substring(0, 10)
            } else {
                ""
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error extracting date from datetime: $dateTime", e)
            ""
        }
    }

    /**
     * Extracts the time part from a datetime string
     * 
     * @param dateTime DateTime string in "yyyy-MM-dd HH:mm:ss" format
     * @return Time part in "HH:mm:ss" format
     */
    fun extractTimeFromDateTime(dateTime: String): String {
        return try {
            if (dateTime.length >= 19) {
                dateTime.substring(11, 19)
            } else if (dateTime.length > 10) {
                dateTime.substring(11)
            } else {
                ""
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error extracting time from datetime: $dateTime", e)
            ""
        }
    }

    /**
     * Validates if a fond caisse amount is valid
     * 
     * @param amount The amount string to validate
     * @return true if amount is valid (not empty, numeric, and > 0), false otherwise
     */
    fun isValidFondCaisseAmount(amount: String): Boolean {
        return try {
            if (amount.isEmpty()) return false
            val numericAmount = amount.toDoubleOrNull() ?: return false
            numericAmount > 0
        } catch (e: Exception) {
            Log.e(TAG, "Error validating fond caisse amount: $amount", e)
            false
        }
    }

    /**
     * Formats a double amount to string with 2 decimal places
     * 
     * @param amount The amount to format
     * @return Formatted amount string
     */
    fun formatAmount(amount: Double): String {
        return try {
            String.format(Locale.getDefault(), "%.2f", amount)
        } catch (e: Exception) {
            Log.e(TAG, "Error formatting amount: $amount", e)
            "0.00"
        }
    }

    /**
     * Parses a string amount to double
     * 
     * @param amount The amount string to parse
     * @return Parsed double value or 0.0 if parsing fails
     */
    fun parseAmount(amount: String): Double {
        return try {
            amount.toDoubleOrNull() ?: 0.0
        } catch (e: Exception) {
            Log.e(TAG, "Error parsing amount: $amount", e)
            0.0
        }
    }

    /**
     * Checks if a session is expired based on date comparison.
     * Sessions are considered expired if the current date differs from the session opening date.
     * Implements traditional POS pattern where sessions expire at midnight.
     *
     * @param sessionOpeningDate The session opening date in "yyyy-MM-dd HH:mm:ss" format
     * @return true if session is expired, false otherwise
     */
    fun isSessionExpired(sessionOpeningDate: String): Boolean {
        if (sessionOpeningDate.isEmpty()) return true

        try {
            val currentDate = getCurrentDate()
            val sessionDate = extractDateFromDateTime(sessionOpeningDate)
            return currentDate != sessionDate
        } catch (e: Exception) {
            Log.e(TAG, "Error checking session expiration", e)
            return true
        }
    }

    /**
     * Checks if two dates are the same (ignoring time)
     *
     * @param date1 First date string
     * @param date2 Second date string
     * @return true if dates are the same, false otherwise
     */
    fun isSameDate(date1: String, date2: String): Boolean {
        return try {
            val extractedDate1 = extractDateFromDateTime(date1)
            val extractedDate2 = extractDateFromDateTime(date2)
            extractedDate1 == extractedDate2
        } catch (e: Exception) {
            Log.e(TAG, "Error comparing dates: $date1, $date2", e)
            false
        }
    }

    /**
     * Calculates the duration in hours between two datetime strings
     * 
     * @param startDateTime Start datetime string
     * @param endDateTime End datetime string (optional, defaults to current time)
     * @return Duration in hours, or -1 if calculation fails
     */
    fun calculateDurationInHours(startDateTime: String, endDateTime: String = getCurrentDateTime()): Long {
        return try {
            val dateFormat = SimpleDateFormat(DATE_FORMAT, Locale.getDefault())
            val startDate = dateFormat.parse(startDateTime)
            val endDate = dateFormat.parse(endDateTime)
            
            if (startDate != null && endDate != null) {
                val durationMillis = endDate.time - startDate.time
                durationMillis / (1000 * 60 * 60) // Convert to hours
            } else {
                -1
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error calculating duration: $startDateTime to $endDateTime", e)
            -1
        }
    }

    /**
     * Generates a user-friendly session duration string
     * 
     * @param startDateTime Session start datetime
     * @param endDateTime Session end datetime (optional, defaults to current time)
     * @return Formatted duration string (e.g., "2h 30m")
     */
    fun formatSessionDuration(startDateTime: String, endDateTime: String = getCurrentDateTime()): String {
        return try {
            val dateFormat = SimpleDateFormat(DATE_FORMAT, Locale.getDefault())
            val startDate = dateFormat.parse(startDateTime)
            val endDate = dateFormat.parse(endDateTime)
            
            if (startDate != null && endDate != null) {
                val durationMillis = endDate.time - startDate.time
                val hours = durationMillis / (1000 * 60 * 60)
                val minutes = (durationMillis % (1000 * 60 * 60)) / (1000 * 60)
                
                when {
                    hours > 0 && minutes > 0 -> "${hours}h ${minutes}m"
                    hours > 0 -> "${hours}h"
                    minutes > 0 -> "${minutes}m"
                    else -> "< 1m"
                }
            } else {
                "Unknown"
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error formatting session duration: $startDateTime to $endDateTime", e)
            "Unknown"
        }
    }
}

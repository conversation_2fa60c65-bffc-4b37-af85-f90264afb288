package com.asmtunis.procaisseinventory.core.session

/**
 * Enumeration representing the possible results of session validation operations.
 * Used throughout the application to determine session state and guide user actions.
 */
enum class SessionValidationResult {
    /**
     * Session is valid and operations can proceed
     */
    VALID,
    
    /**
     * No active session exists - user needs to create a new session
     */
    NO_ACTIVE_SESSION,
    
    /**
     * Session has expired (date changed) - user needs to close expired session and create new one
     */
    SESSION_EXPIRED,
    
    /**
     * Session is closed in database (sCClotCaisse = 1) - user needs to create new session
     */
    SESSION_CLOSED_IN_DB
}

/**
 * Data class representing the current session state with validation information
 */
data class SessionState(
    val isActive: Boolean = false,
    val sessionId: String = "",
    val openingDate: String = "",
    val userId: String = "",
    val caisseCode: String = "",
    val fondCaisse: String = "",
    val validationResult: SessionValidationResult = SessionValidationResult.NO_ACTIVE_SESSION,
    val errorMessage: String? = null
)

/**
 * Sealed class representing session operation results
 */
sealed class SessionOperationResult {
    object Success : SessionOperationResult()
    data class Error(val message: String) : SessionOperationResult()
    object Loading : SessionOperationResult()
}

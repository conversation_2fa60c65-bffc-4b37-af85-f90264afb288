package com.asmtunis.procaisseinventory.core.model

import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.JsonElement

@Serializable
data class GenericObject(
    val connexion: BaseConfig,
    //@Serializable(with = DynamicLookupSerializer::class)
    @SerialName("object")
    val data: JsonElement?
)
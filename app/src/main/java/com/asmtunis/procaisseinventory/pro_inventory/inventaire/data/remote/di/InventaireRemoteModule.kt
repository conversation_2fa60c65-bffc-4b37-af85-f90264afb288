package com.asmtunis.procaisseinventory.pro_inventory.inventaire.data.remote.di

import com.asmtunis.procaisseinventory.pro_inventory.inventaire.data.remote.api.inv.InventaireApi
import com.asmtunis.procaisseinventory.pro_inventory.inventaire.data.remote.api.inv.InventaireApiImpl
import com.asmtunis.procaisseinventory.pro_inventory.inventaire.data.remote.api.ligne_inv.LigneInventaireApi
import com.asmtunis.procaisseinventory.pro_inventory.inventaire.data.remote.api.ligne_inv.LigneInventaireApiImpl
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import io.ktor.client.HttpClient
import javax.inject.Singleton


@Module
    @InstallIn(SingletonComponent::class)
    object InventaireRemoteModule {

        @Provides
        @Singleton
        fun provideInventaireApi(client: HttpClient): InventaireApi = InventaireApiImpl(client)




        @Provides
        @Singleton
        fun provideLigneInventaireApi(client: HttpClient): LigneInventaireApi = LigneInventaireApiImpl(client)

    }
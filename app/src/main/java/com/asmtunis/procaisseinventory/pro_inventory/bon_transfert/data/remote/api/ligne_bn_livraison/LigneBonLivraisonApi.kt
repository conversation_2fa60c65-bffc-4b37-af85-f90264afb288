package com.asmtunis.procaisseinventory.pro_inventory.bon_transfert.data.remote.api.ligne_bn_livraison

import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.pro_inventory.bon_transfert.data.domaine.LigneBonLivraison
import kotlinx.coroutines.flow.Flow


interface LigneBonLivraisonApi {

        suspend fun getLigneBonLivraison(baseConfig: String): Flow<DataResult<List<LigneBonLivraison>>>
}
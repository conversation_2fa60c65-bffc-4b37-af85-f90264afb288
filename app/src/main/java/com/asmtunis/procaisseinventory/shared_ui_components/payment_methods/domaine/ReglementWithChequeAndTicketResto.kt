package com.asmtunis.procaisseinventory.shared_ui_components.payment_methods.domaine

import androidx.room.Embedded
import androidx.room.Relation
import com.asmtunis.procaisseinventory.data.cheque_caisse.domaine.ChequeCaisse
import com.asmtunis.procaisseinventory.data.ticket_resto.domaine.TraiteCaisse
import com.asmtunis.procaisseinventory.pro_caisse.reglement.data.domaine.ReglementCaisse
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class ReglementWithChequeAndTicketResto (
    @SerialName("reglement")
    @Embedded
    val reglementCaisse : ReglementCaisse,

    @SerialName("cheques")
    @Relation(
     parentColumn = "REGC_Code",
        entityColumn = "Reglement"
    )
    val cheques : List<ChequeCaisse>,

    @SerialName("traites")
    @Relation(
        parentColumn = "REGC_Code",
        entityColumn = "TRAIT_Reglement"
    )
    val ticketsResto : List<TraiteCaisse>
)
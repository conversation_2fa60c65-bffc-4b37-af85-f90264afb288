package com.asmtunis.procaisseinventory.shared_ui_components.cameraview

import android.content.Context
import android.content.pm.PackageManager
import com.asmtunis.procaisseinventory.core.local_storage.datastore.viewmodel.DataViewModel
import com.asmtunis.procaisseinventory.core.utils.PackageUtils.missingSystemFeature

object CameraUtils {

     fun checkCamera(activity: Context, dataViewModel: DataViewModel) {
        //checking if device has camera
        if (activity.packageManager.missingSystemFeature(PackageManager.FEATURE_CAMERA_ANY)) {
            //camera not found.
            dataViewModel.saveHaveCameraDevice(false)
        } else {
            dataViewModel.saveHaveCameraDevice(true)
        }
    }
}
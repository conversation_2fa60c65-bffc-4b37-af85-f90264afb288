package com.asmtunis.procaisseinventory.shared_ui_components.payment_methods.ticket_resto

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Home
import androidx.compose.material3.Button
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.Text
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.core.UiText
import com.asmtunis.procaisseinventory.data.carte_resto.domaine.CarteResto
import com.asmtunis.procaisseinventory.shared_ui_components.LottieAnim
import com.simapps.ui_kit.drop_down_menu.GenericDropdownMenu
import com.simapps.ui_kit.edit_text.EditTextField
import kotlinx.coroutines.launch
import java.util.Locale


@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun TicketRestoInputView(
    montantTicketResto :String,
    montantTicketRestoError :UiText?,
    onMontantTicketRestoChange: (String) -> Unit,
    ticketRestoNbr: String,
    ticketRestoNbrError: UiText?,
    onTicketRestoNbrChange: (String) -> Unit,
    taux : String,
    tauxError : UiText?,
    onTicketRestoTauxChange: (String) -> Unit,
    onDismissRequest: () -> Unit,
    onAddClicked: () -> Unit,
    ticketRestoList: List<CarteResto>,
    onTicketRestoExpandChange:(Boolean)->Unit,
    isTicketRestoExpanded: Boolean,
    selectedTicketResto: CarteResto,
    selectedTicketRestoError: UiText?,
    onSelectedTicketRestoChange: (CarteResto)->Unit


) {
    val sheetState = rememberModalBottomSheetState()
    val scope = rememberCoroutineScope()
    var ticketRestaurantFilter by rememberSaveable { mutableStateOf("") }

    Dialog(
        onDismissRequest = {
            // Dismiss the dialog when the user clicks outside the dialog or on the back
            // button. If you want to disable that functionality, simply use an empty
            // onDismissRequest.
            onDismissRequest()

        },
        properties = DialogProperties(
            usePlatformDefaultWidth = true
        ),
        content = {
            Card(
                elevation = CardDefaults.cardElevation(),
                shape = RoundedCornerShape(15.dp),
            ) {
                Column(
                    modifier = Modifier.fillMaxWidth().padding(12.dp),
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.Top
                ) {


                    Text(
                        text = stringResource(R.string.resto_ticket),
                       // color = MaterialTheme.colorScheme.outline,
                        fontSize = MaterialTheme.typography.titleLarge.fontSize
                    )

                    Spacer(modifier = Modifier.height(16.dp))
                    EditTextField(
                        modifier = Modifier.fillMaxWidth(0.95f),
                        requestFocus = true,
                        text = ticketRestoNbr,
                        errorValue = ticketRestoNbrError?.asString(),
                        label = stringResource(R.string.number1_field_title),
                        onValueChange = {
                            onTicketRestoNbrChange(it)
                        },
                        readOnly = false,
                        showTrailingIcon = true,
                        //   enabled = distNumViewModel.modifyVisite,
                        leadingIcon = Icons.Default.Home,
                        showLeadingIcon  = false,
                        keyboardType = KeyboardType.Decimal,
                        imeAction = ImeAction.Next,
                        onKeyboardActions = {
                            //  scope.launch {
                            //      sheetState.hide()
                            //  }
                            //   onDismissRequest()
                        },
                    )
                    Spacer(modifier = Modifier.height(9.dp))
                    EditTextField(
                        modifier = Modifier.fillMaxWidth(0.95f),
                        text = if(montantTicketResto=="0.0") ""
                        else montantTicketResto,
                        errorValue = montantTicketRestoError?.asString(),
                        label = stringResource(R.string.amount_field_title),
                        onValueChange = {
                            onMontantTicketRestoChange(it)
                        },
                        readOnly = false,
                        //   enabled = distNumViewModel.modifyVisite,
                        leadingIcon = Icons.Default.Home,
                        showLeadingIcon  = false,
                        keyboardType = KeyboardType.Decimal,
                        imeAction = ImeAction.Next,
                        onKeyboardActions = {
                            //  scope.launch {
                            //      sheetState.hide()
                            //  }
                            //   onDismissRequest()
                        },
                    )
                    Spacer(modifier = Modifier.height(9.dp))
                    EditTextField(
                        modifier = Modifier.fillMaxWidth(0.95f),
                        text = taux,
                        errorValue = tauxError?.asString(),
                        label = stringResource(R.string.taux),
                        onValueChange = {
                            onTicketRestoTauxChange(it)
                        },
                        readOnly = false,
                        //   enabled = distNumViewModel.modifyVisite,
                        leadingIcon = Icons.Default.Home,
                        showLeadingIcon  = false,
                        showTrailingIcon = true,
                        keyboardType = KeyboardType.Decimal,
                        imeAction = ImeAction.Next,
                        onKeyboardActions = {
                            //  scope.launch {
                            //      sheetState.hide()
                            //  }
                            //   onDismissRequest()
                        },
                    )
                    Spacer(modifier = Modifier.height(9.dp))
                    GenericDropdownMenu (
                        modifier = Modifier.fillMaxWidth(0.95f),
                        fiterValue = ticketRestaurantFilter,
                        onFilterValueChange = { ticketRestaurantFilter = it },
                        designation =  selectedTicketResto.societe,
                        errorValue = selectedTicketRestoError?.asString(),
                        label = stringResource(R.string.designation_ticket_restaurant),
                        readOnly = true,
                        showFilter = true,
                        itemList = if(ticketRestaurantFilter.isNotEmpty()) ticketRestoList.filter { it.societe.lowercase(Locale.ROOT).contains(ticketRestaurantFilter.lowercase(Locale.ROOT)) } else ticketRestoList,
                        itemExpanded = isTicketRestoExpanded,
                        selectedItem = selectedTicketResto,
                        getItemDesignation = { it.societe },
                        getItemTrailing = { it.code },
                        onClick = {
                            /*  textValidationViewModel.onAddNewEvent(
                                  InventoryFormEvent.stationChanged(
                                      itemValue
                                  )
                              )*/
                            onSelectedTicketRestoChange(it)
                            onTicketRestoExpandChange(false)
                        },
                        onItemExpandedChange = {
                            onTicketRestoExpandChange(it)
                        },
                        lottieAnimEmpty = {
                            LottieAnim(lotti = R.raw.emptystate)
                        },
                        lottieAnimError = {
                            LottieAnim(lotti = R.raw.connection_error, size = it)
                        }
                    )








                    Spacer(modifier = Modifier.height(16.dp))

                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceAround,
                        verticalAlignment = Alignment.CenterVertically
                    ) {




                        OutlinedButton(
                            onClick = {
                                onDismissRequest()
                            }
                        ) {
                            Text(text = stringResource(R.string.quitter))
                        }


                         Button(
                            enabled = montantTicketResto!="" && ticketRestoNbr!=""
                                    && taux!=""
                                    &&  selectedTicketResto!= CarteResto(),
                            onClick = {
                                scope.launch {
                                    sheetState.hide()
                                }
                                onAddClicked()
                            }
                        ) {
                            Text(text = stringResource(R.string.OK))
                        }
                    }

                }
            }


            }
    )
    }

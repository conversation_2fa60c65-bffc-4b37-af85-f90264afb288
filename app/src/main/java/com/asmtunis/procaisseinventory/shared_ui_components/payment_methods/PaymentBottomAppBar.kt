package com.asmtunis.procaisseinventory.shared_ui_components.payment_methods

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutVertically
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.width
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Save
import androidx.compose.material3.BottomAppBar
import androidx.compose.material3.BottomAppBarDefaults
import androidx.compose.material3.FloatingActionButton
import androidx.compose.material3.FloatingActionButtonDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.R

@Composable
fun PaymentBottomAppBar (
    onTicketClick: () -> Unit,
    onChequeClick: () -> Unit,
    onEspeceClick: () -> Unit,
    onFloatingActionButtonClick: () -> Unit,
    floatingActionButtonVisibility : Boolean,

){

    BottomAppBar(
        actions = {
            Spacer(modifier = Modifier.width(12.dp))

            Text(
                text = stringResource(id = R.string.ticket_title),
                color = MaterialTheme.colorScheme.outline,
                fontWeight = MaterialTheme.typography.titleMedium.fontWeight,
                modifier = Modifier.clickable {
                    onTicketClick()
                }
            )
            Spacer(modifier = Modifier.width(12.dp))
           
            Text(
                text = stringResource(id = R.string.espece),
                color = MaterialTheme.colorScheme.outline,
                fontWeight = MaterialTheme.typography.titleMedium.fontWeight,
                modifier = Modifier.clickable {
                    onEspeceClick()
                }
            )
            Spacer(modifier = Modifier.width(12.dp))
           
            Text(
                text = stringResource(id = R.string.checK),
                color = MaterialTheme.colorScheme.outline,
                fontWeight = MaterialTheme.typography.titleMedium.fontWeight,
                modifier = Modifier.clickable {
                    onChequeClick()
                }
            )
           
        },
        floatingActionButton = {
            AnimatedVisibility(
                visible = floatingActionButtonVisibility,
                enter = fadeIn() + slideInVertically(),
                exit = fadeOut() + slideOutVertically()
            ) {
                FloatingActionButton(
                    onClick = {
                        onFloatingActionButtonClick()
                    },
                    containerColor = BottomAppBarDefaults.bottomAppBarFabColor,
                    elevation = FloatingActionButtonDefaults.bottomAppBarFabElevation()
                ) {
                    Icon(Icons.Filled.Save, "Localized description")
                }
            }

        }
    )
}
package com.asmtunis.procaisseinventory.pro_caisse.tournee.data.remote.api

import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.pro_caisse.tournee.data.domaine.ChangeLigneOrdreMission
import com.asmtunis.procaisseinventory.pro_caisse.tournee.data.domaine.EtatOrdreMission
import com.asmtunis.procaisseinventory.pro_caisse.tournee.data.domaine.OrdreMissionWithLines
import kotlinx.coroutines.flow.Flow

interface OrdreMissionApi {

    suspend fun getOrdreMissionWithLines(baseConfig: String): Flow<DataResult<List<OrdreMissionWithLines>>>
    suspend fun getEtatOrdreMission(baseConfig: String): Flow<DataResult<List<EtatOrdreMission>>>

    suspend fun updateLigneOrdreMission(baseConfig: String): Flow<DataResult<Boolean>>
    suspend fun addOrdreMission(baseConfig: String): Flow<DataResult<Boolean>>
    suspend fun batchUpdateLigneOrdreMission(baseConfig: String): Flow<DataResult<List<ChangeLigneOrdreMission>>>


}
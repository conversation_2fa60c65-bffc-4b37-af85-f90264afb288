package com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.dep_out_byuser.data.domaine

import androidx.room.Embedded
import androidx.room.Relation
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.data.domaine.Immobilisation
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable


@Serializable
data class DeplacementOutByUserWithImmobilisation(
    @Embedded
    @SerialName("DeplacementOutByUser")
    var deplacementOutByUser: DeplacementOutByUser? = null,



    @Relation(
        parentColumn = "DEV_CodeClient",
        entityColumn = "CLI_Code"
    )
    @SerialName("Immobilisation")
    var immobilisation: Immobilisation? = null,
)
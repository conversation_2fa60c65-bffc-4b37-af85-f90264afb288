package com.asmtunis.procaisseinventory.pro_caisse.tournee.data.local.etat_ordre_mission.repository

import com.asmtunis.procaisseinventory.pro_caisse.tournee.data.domaine.EtatOrdreMission
import com.asmtunis.procaisseinventory.pro_caisse.tournee.data.local.etat_ordre_mission.dao.EtatOrdreMissionDAO
import kotlinx.coroutines.flow.Flow


class EtatOrdreMissionLocalRepositoryImpl(
        private val etatOrdreMissionDAO: EtatOrdreMissionDAO
    ) : EtatOrdreMissionLocalRepository {
    override fun upsertAll(value: List<EtatOrdreMission>) = etatOrdreMissionDAO.insertAll(value)


    override fun getOneByCode(code: String): Flow<EtatOrdreMission?> = etatOrdreMissionDAO.getOne(code)

    override fun getAll(): Flow<List<EtatOrdreMission>?> = etatOrdreMissionDAO.all

    override fun deleteAll() = etatOrdreMissionDAO.deleteAll()
}
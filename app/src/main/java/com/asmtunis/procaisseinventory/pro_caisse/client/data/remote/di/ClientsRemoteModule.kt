package com.asmtunis.procaisseinventory.pro_caisse.client.data.remote.di

import com.asmtunis.procaisseinventory.pro_caisse.client.data.remote.api.ClientsApi
import com.asmtunis.procaisseinventory.pro_caisse.client.data.remote.api.ClientsApiImpl
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import io.ktor.client.HttpClient
import javax.inject.Singleton


@Module
    @InstallIn(SingletonComponent::class)
    object ClientsRemoteModule {

        @Provides
        @Singleton
        fun provideClientApi(client: HttpClient): ClientsApi = ClientsApiImpl(client)


    }
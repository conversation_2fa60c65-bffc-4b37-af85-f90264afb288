package com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.text_validation

import android.util.Log
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.asmtunis.procaisseinventory.core.textvalidation.use_cases.ValidateDoubleNotZero
import com.asmtunis.procaisseinventory.core.textvalidation.use_cases.ValidateEmail
import com.asmtunis.procaisseinventory.core.textvalidation.use_cases.ValidateList
import com.asmtunis.procaisseinventory.core.textvalidation.use_cases.ValidatePassword
import com.asmtunis.procaisseinventory.core.textvalidation.use_cases.ValidatePhoneNumber
import com.asmtunis.procaisseinventory.core.textvalidation.use_cases.ValidateStringNotEmpty
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.domaine.FamilleDn
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.data.domaine.ConcurrentVC
import kotlinx.coroutines.launch


class VisiteTextValidationViewModel(
    private val validateIsNotEmptyString: ValidateStringNotEmpty = ValidateStringNotEmpty(),
    private val validateList: ValidateList = ValidateList(),
    private val validateEmail: ValidateEmail = ValidateEmail(),
    private val validatePassword: ValidatePassword = ValidatePassword(),
    private val validatePhoneNumber: ValidatePhoneNumber = ValidatePhoneNumber(),
    private val validateDoubleNotZero: ValidateDoubleNotZero = ValidateDoubleNotZero()
) : ViewModel() {


    /**
     * Add VISITE input edit text validation
     */

    var stateAddVisite by mutableStateOf(AddVisiteFormState())


    var validationAddVisiteEvents by mutableStateOf(ValidationAddVisiteEvent())


    fun restStateAddVisite() {
        stateAddVisite = AddVisiteFormState()
    }


    fun restStateAddVisiteFamilleProduit() {
        stateAddVisite = stateAddVisite.copy(
            familleProduit = FamilleDn(),
            fournisseur = ConcurrentVC(),
            remarque = ""
        )
    }

    fun onAddVisiteEvent(event: AddVisiteFormEvent) {
        when (event) {
            is AddVisiteFormEvent.AdresseChanged ->
                stateAddVisite = stateAddVisite.copy(adresse = event.adresse)

            is AddVisiteFormEvent.DelegationChanged ->
                stateAddVisite = stateAddVisite.copy(delegation = event.delegation)

            is AddVisiteFormEvent.GouvernoratChanged ->
                stateAddVisite = stateAddVisite.copy(gouvernorat = event.gouvernorat)

            is AddVisiteFormEvent.LatitudeChanged ->
                stateAddVisite = stateAddVisite.copy(latitude = event.latitude)

            is AddVisiteFormEvent.LongitudeChanged ->
                stateAddVisite = stateAddVisite.copy(longitude = event.longitude)

            is AddVisiteFormEvent.NomGerantChanged ->
                stateAddVisite = stateAddVisite.copy(
                    nomGerant = event.nomGerant,
                    nomGerantError = validateIsNotEmptyString.execute(event.nomGerant).errorMessage
                )

            is AddVisiteFormEvent.PhoneNumber1Changed ->
                stateAddVisite = stateAddVisite.copy(phone1 = event.phonenumber1)

            is AddVisiteFormEvent.NomMagasinChanged ->
                stateAddVisite = stateAddVisite.copy(nomMagasin = event.nomMagasin)


            AddVisiteFormEvent.SubmitAddVisite ->
                submitAddVisiteData()

            is AddVisiteFormEvent.SuperficieChanged ->
                stateAddVisite = stateAddVisite.copy(
                    superficie = event.superficie,
                    superficieError = validateIsNotEmptyString.execute(event.superficie.codeSuperf).errorMessage
                )

            is AddVisiteFormEvent.TypePtVenteChanged ->
                stateAddVisite = stateAddVisite.copy(
                    typePtVente = event.typePtVente,
                    typePtVenteError = validateIsNotEmptyString.execute(event.typePtVente.codeTypePV).errorMessage
                )

            is AddVisiteFormEvent.TypeServiceChanged ->
                stateAddVisite = stateAddVisite.copy(
                    typeService = event.typeService,
                    typeServiceError = validateIsNotEmptyString.execute(event.typeService.codeTypeSv).errorMessage
                )

            is AddVisiteFormEvent.FamilleProduitChanged -> {

                stateAddVisite = stateAddVisite.copy(
                    familleProduit = event.familleProduit,
                   // familleProduitError =  validateIsNotEmptyString.execute(event.familleProduit.codeFamille).errorMessage
                )
            }

            is AddVisiteFormEvent.CountryDataChanged ->

                stateAddVisite = stateAddVisite.copy(countryData = event.countryData)

            is AddVisiteFormEvent.FournisseurChanged -> stateAddVisite = stateAddVisite.copy(
                fournisseur = event.fournisseur,
               // fournisseurError =  validateIsNotEmptyString.execute(event.fournisseur.codeconcurrent).errorMessage
                )

            is AddVisiteFormEvent.LigneVisitesDnChanged -> stateAddVisite = stateAddVisite.copy(
                ligneVisitesDn = event.ligneVisitesDn,
                ligneVisitesDnError = validateList.execute(event.ligneVisitesDn).errorMessage
            )

            is AddVisiteFormEvent.RemarqueChanged -> stateAddVisite = stateAddVisite.copy(remarque = event.remarque)
        }
    }

    private fun submitAddVisiteData() {
        val nomGerantResult = validateIsNotEmptyString.execute(stateAddVisite.nomGerant)

        val addresseResult = validateIsNotEmptyString.execute(stateAddVisite.adresse)
        val nomMagasinResult = validateIsNotEmptyString.execute(stateAddVisite.nomMagasin)


        val superficieResult = validateIsNotEmptyString.execute(stateAddVisite.superficie.codeSuperf)
        val typePtVenteResult = validateIsNotEmptyString.execute(stateAddVisite.typePtVente.codeTypePV)
        val typeServiceResult = validateIsNotEmptyString.execute(stateAddVisite.typeService.codeTypeSv)



       val ligneVisitesDnResult = validateList.execute(stateAddVisite.ligneVisitesDn)

        val hasError = listOf(
            nomGerantResult,
            addresseResult,
            nomMagasinResult,
            superficieResult,
            typePtVenteResult,
            typeServiceResult,
            ligneVisitesDnResult
        ).any { !it.successful }

        Log.d("sgdsdsdg", "hasError " +hasError)

        Log.d("sgdsdsdg", "nomGerantResult " +nomGerantResult.errorMessage)
            Log.d("sgdsdsdg", "addresseResult " + addresseResult.errorMessage)
                Log.d("sgdsdsdg", "nomMagasinResult " + nomMagasinResult.errorMessage)

                    Log.d("sgdsdsdg", "superficieResult " + superficieResult.errorMessage)
                        Log.d("sgdsdsdg", "typeServiceResult " + typeServiceResult.errorMessage)
                            Log.d("sgdsdsdg", "typePtVenteResult " + typePtVenteResult.errorMessage)
                                Log.d("sgdsdsdg", "ligneVisitesDnResult " + ligneVisitesDnResult.errorMessage)
        if (hasError) {
            stateAddVisite = stateAddVisite.copy(
                nomGerantError = nomGerantResult.errorMessage,
                adresseError = addresseResult.errorMessage,
                nomMagasinError = nomMagasinResult.errorMessage,

                superficieError = superficieResult.errorMessage,
                typeServiceError = typeServiceResult.errorMessage,
                typePtVenteError = typePtVenteResult.errorMessage,
                ligneVisitesDnError = ligneVisitesDnResult.errorMessage
                )
            return
        }
        viewModelScope.launch {


            validationAddVisiteEvents = ValidationAddVisiteEvent.AddVisite(stateAddVisite)
        }
    }



    private fun submitAddFamilleItem() {

         val familleProduiteResult = validateIsNotEmptyString.execute(stateAddVisite.familleProduit.codeFamille)
         val fournisseurResult = validateIsNotEmptyString.execute(stateAddVisite.fournisseur.codeconcurrent)


        val hasError = listOf(
            familleProduiteResult,
            fournisseurResult
        ).any { !it.successful }

        if (hasError) {
            stateAddVisite = stateAddVisite.copy(
                familleProduitError = familleProduiteResult.errorMessage,
                fournisseurError = familleProduiteResult.errorMessage

            )
            return
        }
        viewModelScope.launch {


            validationAddVisiteEvents = ValidationAddVisiteEvent.AddVisite(stateAddVisite)
        }
    }
}
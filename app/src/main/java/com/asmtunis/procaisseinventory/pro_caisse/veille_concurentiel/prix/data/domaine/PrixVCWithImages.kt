package com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.prix.data.domaine

import androidx.room.Embedded
import androidx.room.Relation
import com.asmtunis.procaisseinventory.data.image_piece_joint.domaine.ImagePieceJoint
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable


@Serializable
data class PrixVCWithImages (
    @Embedded
    @SerialName("PrixVC")
    var prixVC: PrixVC? = null,

    @Relation(
        parentColumn = "CodeVCPrixM",
        entityColumn = "Code_TypeVC"
    )
    @SerialName("ImagePieceJoint")
    var imageList: List<ImagePieceJoint>? = null,
)
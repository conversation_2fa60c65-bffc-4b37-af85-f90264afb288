package com.asmtunis.procaisseinventory.articles.data.article.local.repository.article_codebar

import com.asmtunis.procaisseinventory.articles.data.article.domaine.ArticleCodeBar
import com.asmtunis.procaisseinventory.articles.data.article.local.dao.ArticleCodeBarDAO
import kotlinx.coroutines.flow.Flow


class ArticleBarCodeLocalRepositoryImpl(
        private val articleCodeBarDAO: ArticleCodeBarDAO
    ) : ArticleBarCodeLocalRepository {
    override fun upsert(value: ArticleCodeBar) =
        articleCodeBarDAO.insert(value)

    override fun upsertAll(value: List<ArticleCodeBar>)  =
        articleCodeBarDAO.insertAll(value)

    override fun deleteAll()  =
        articleCodeBarDAO.deleteAll()

    override fun getAll(): Flow<List<ArticleCodeBar>>  =
        articleCodeBarDAO.all
}
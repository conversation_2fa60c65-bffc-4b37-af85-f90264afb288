package com.asmtunis.procaisseinventory.data.tva.local.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProInventoryConstants.Companion.TVA_TABLE
import com.asmtunis.procaisseinventory.data.tva.domaine.Tva
import kotlinx.coroutines.flow.Flow


@Dao
interface TvaDAO {
    @get:Query("SELECT * FROM $TVA_TABLE")
    val all: Flow<List<Tva>?>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertAll(items: List<Tva>)

    @Query("DELETE FROM Tva")
    fun deleteAll()
}
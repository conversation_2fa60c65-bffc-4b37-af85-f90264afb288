package com.asmtunis.procaisseinventory.data.prefixe.local.repository

import com.asmtunis.procaisseinventory.data.prefixe.domaine.Prefixe
import com.asmtunis.procaisseinventory.data.prefixe.local.dao.PrefixeDAO
import kotlinx.coroutines.flow.Flow



class PrefixeLocalRepositoryImpl(
    private val prefixDAO: PrefixeDAO
) : PrefixeLocalRepository {

    override fun upsertAll(value: List<Prefixe>) = prefixDAO.insertAll(value)

    override fun deleteAll() = prefixDAO.deleteAll()

    override fun getAll(): Flow<List<Prefixe>> = prefixDAO.all
    override fun getOneById(code: String): Flow<Prefixe?>  = prefixDAO.getOneById(code)
}
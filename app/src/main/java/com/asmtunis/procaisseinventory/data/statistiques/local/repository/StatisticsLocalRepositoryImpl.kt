package com.asmtunis.procaisseinventory.data.statistiques.local.repository

import com.asmtunis.procaisseinventory.data.statistiques.domaine.Statistics
import com.asmtunis.procaisseinventory.data.statistiques.local.dao.StatisticsDAO
import kotlinx.coroutines.flow.Flow


class StatisticsLocalRepositoryImpl(private val statisticsDAO: StatisticsDAO) : StatisticsLocalRepository {

    override fun upsertAll(value: Statistics) = statisticsDAO.insertAll(value)

    override fun deleteAll() = statisticsDAO.deleteAll()

    override fun getAll(): Flow<Statistics> = statisticsDAO.all
}